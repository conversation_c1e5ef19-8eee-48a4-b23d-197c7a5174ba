import bcrypt from 'bcryptjs'
import { and, asc, count, desc, eq, ilike, or, sql } from 'drizzle-orm'
import { initializeDatabase, initializeDatabaseLocal } from '../db/connection'
import {
  bots,
  botUsages,
  chatMessages,
  chatSessions,
  collections,
  contacts,
  documents,
  facebookConfigs,
  facebookMessages,
  handoverRequests,
  s3Configurations,
  services,
  sessionAssignments,
  sites,
  twilioConfigs, // Added twilioConfigs schema
  twilioMessages, // Added twilioMessages schema
  users,
  whatsappConfigs,
  whatsappMessages,
} from '../db/schema'
import type {
  AdminUser,
  AgentUser,
  Bot,
  BotWithUsage,
  ChatSession,
  Contact,
  ContactCreationRequest,
  ContactUpdateRequest,
  FacebookConfig,
  HandoverRequest,
  S3Configuration,
  S3ConfigurationCreationRequest,
  ServiceConfiguration,
  ServiceCreationRequest,
  ServiceUpdateRequest,
  TwilioDbConfig, // Added TwilioDbConfig type
  TwilioDbMessage, // Added TwilioDbMessage type
  User,
  UserCreationRequest,
  UserUpdateRequest,
  WhatsAppConfig,
} from '../types'
import { UserRole } from '../types'

export class DatabaseService {
  private env?: Record<string, unknown>
  private isCloudflareWorkers: boolean
  private db: ReturnType<typeof initializeDatabase> | null = null

  constructor(env?: Record<string, unknown>) {
    this.env = env

    // Check if we're running in Cloudflare Workers environment
    // In Cloudflare Workers, we have access to env object and specific globals
    this.isCloudflareWorkers =
      env &&
      typeof env === 'object' &&
      (typeof globalThis.caches !== 'undefined' ||
        typeof globalThis.Response !== 'undefined')

    // Validate that DATABASE_URL is available
    if (
      !env?.DATABASE_URL &&
      !(typeof process !== 'undefined' && process.env.DATABASE_URL)
    ) {
      throw new Error(
        'DATABASE_URL is required. Set it in .env file for both development and production.',
      )
    }

    // Initialize database connection
    this.db = this.getDb()

    // Initialize tables asynchronously
    this.initializeTables().catch((error) => {
      console.error('Failed to initialize tables:', error)
    })
  }

  /**
   * Create a fresh database connection for each operation to avoid I/O context issues
   */
  private getDb(): ReturnType<typeof initializeDatabase> {
    if (this.env?.DATABASE_URL) {
      // console.log(
      //   'Using PostgreSQL database with Drizzle ORM (from Workers env)',
      // );
      return initializeDatabase(this.env)
    }
    if (typeof process !== 'undefined' && process.env.DATABASE_URL) {
      // console.log(
      //   'Using PostgreSQL database with Drizzle ORM (from process.env)',
      // );
      return initializeDatabaseLocal()
    }
    throw new Error(
      'DATABASE_URL is required. For Cloudflare Workers, set it in wrangler.toml or via secrets. For local development, set it in .env file.',
    )
  }

  private async initializeTables(): Promise<void> {
    try {
      // Tables are created via Drizzle migrations, but we still need to create default admin
      // Create default admin user if none exists
      await this.createDefaultAdmin()
    } catch (error) {
      console.error('Failed to initialize tables:', error)
    }
  }

  private async createDefaultAdmin(): Promise<void> {
    try {
      const db = this.getDb()

      const adminCount = await db
        .select({ count: count() })
        .from(users)
        .where(sql`'ADMIN' = ANY(${users.roles})`)

      if (adminCount[0]?.count === 0) {
        const defaultPassword = process.env.ADMIN_DEFAULT_PASSWORD || 'admin123'
        const hashedPassword = bcrypt.hashSync(defaultPassword, 10)

        await db.insert(users).values({
          siteId: 1, // Default site
          username: 'admin',
          passwordHash: hashedPassword,
          roles: ['ADMIN'],
          isActive: true,
        })

        console.log(
          'Default admin user created with username: admin and roles: [ADMIN]',
        )
        console.log(`Default password: ${defaultPassword}`)
      }
    } catch (error) {
      console.error('Error creating default admin:', error)
    }
  }

  // WhatsApp Configuration Methods
  async getWhatsAppConfig(
    siteIdInput?: string | number,
  ): Promise<WhatsAppConfig | null> {
    // Added siteIdInput
    try {
      const db = this.getDb()
      const siteId =
        typeof siteIdInput === 'string'
          ? Number.parseInt(siteIdInput, 10)
          : siteIdInput

      if (siteId === undefined || Number.isNaN(siteId)) {
        console.warn(
          'getWhatsAppConfig called without a valid siteId. This might fetch a global config if not intended.',
        )
        // Fallback to old behavior if no siteId, or handle error
        const config = await db
          .select()
          .from(whatsappConfigs)
          .where(eq(whatsappConfigs.isActive, true))
          .orderBy(desc(whatsappConfigs.createdAt))
          .limit(1)
        if (!config[0]) return null
        return config[0] as WhatsAppConfig // Assuming type compatibility
      }

      const config = await db
        .select()
        .from(whatsappConfigs)
        .where(
          and(
            eq(whatsappConfigs.isActive, true),
            eq(whatsappConfigs.siteId, siteId),
          ),
        )
        .orderBy(desc(whatsappConfigs.createdAt))
        .limit(1)

      if (!config[0]) {
        return null
      }
      // Ensure all fields from WhatsAppConfig type are returned
      return {
        id: config[0].id,
        accessToken: config[0].accessToken,
        phoneNumberId: config[0].phoneNumberId,
        webhookVerifyToken: config[0].webhookVerifyToken,
        businessAccountId: config[0].businessAccountId || undefined, // Ensure optional fields are handled
        isActive: config[0].isActive,
        createdAt: config[0].createdAt,
        updatedAt: config[0].updatedAt,
        // siteId: config[0].siteId, // Add if WhatsAppConfig type includes siteId
      }
    } catch (error) {
      console.error('Error getting WhatsApp config:', error)
      return null
    }
  }

  async saveWhatsAppConfig(
    // siteId must be part of the config object
    config: Omit<WhatsAppConfig, 'id' | 'createdAt' | 'updatedAt'> & {
      siteId: number
    },
  ): Promise<number> {
    try {
      const db = this.getDb()

      // Deactivate existing configs for the specific siteId
      await db
        .update(whatsappConfigs)
        .set({ isActive: false })
        .where(eq(whatsappConfigs.siteId, config.siteId))

      // Insert new config
      const newConfig = await db
        .insert(whatsappConfigs)
        .values({
          siteId: 1, // Default site
          accessToken: config.accessToken,
          phoneNumberId: config.phoneNumberId,
          webhookVerifyToken: config.webhookVerifyToken,
          businessAccountId: config.businessAccountId || null,
        })
        .returning({ id: whatsappConfigs.id })

      return newConfig[0].id
    } catch (error) {
      console.error('Error saving WhatsApp config:', error)
      return 0
    }
  }

  // Twilio Configuration Methods
  async getTwilioConfig(
    siteIdInput: string | number,
  ): Promise<TwilioDbConfig | null> {
    try {
      const db = this.getDb()
      const siteId =
        typeof siteIdInput === 'string'
          ? Number.parseInt(siteIdInput, 10)
          : siteIdInput

      if (Number.isNaN(siteId)) {
        console.error('getTwilioConfig: Invalid siteId provided.')
        return null
      }

      const result = await db
        .select()
        .from(twilioConfigs)
        .where(
          and(
            eq(twilioConfigs.isActive, true),
            eq(twilioConfigs.siteId, siteId),
          ),
        )
        .orderBy(desc(twilioConfigs.createdAt))
        .limit(1)

      if (!result[0]) {
        return null
      }
      return result[0] as TwilioDbConfig
    } catch (error) {
      console.error(
        `Error getting Twilio config for siteId ${siteIdInput}:`,
        error,
      )
      return null
    }
  }

  async saveTwilioConfig(
    config: Omit<TwilioDbConfig, 'id' | 'createdAt' | 'updatedAt'>, // siteId is part of TwilioDbConfig
  ): Promise<number> {
    try {
      const db = this.getDb()

      if (config.siteId === undefined) {
        throw new Error('siteId is required to save Twilio configuration.')
      }

      // Deactivate existing active configs for the specific siteId
      await db
        .update(twilioConfigs)
        .set({ isActive: false })
        .where(
          and(
            eq(twilioConfigs.siteId, config.siteId),
            eq(twilioConfigs.isActive, true),
          ),
        )

      // Insert new config, ensuring it's active
      const newConfig = await db
        .insert(twilioConfigs)
        .values({
          ...config,
          isActive: true, // Ensure new config is active
        })
        .returning({ id: twilioConfigs.id })

      return newConfig[0].id
    } catch (error) {
      console.error('Error saving Twilio config:', error)
      return 0 // Or throw error
    }
  }

  // Facebook Configuration Methods
  async getFacebookConfig(
    siteIdInput?: string | number,
  ): Promise<FacebookConfig | null> {
    try {
      const db = this.getDb()
      const siteId =
        typeof siteIdInput === 'string'
          ? Number.parseInt(siteIdInput, 10)
          : siteIdInput

      if (siteId === undefined || Number.isNaN(siteId)) {
        console.warn(
          'getFacebookConfig called without a valid siteId. This might fetch a global config if not intended.',
        )
        // Fallback to old behavior if no siteId, or handle error
        const config = await db
          .select()
          .from(facebookConfigs)
          .where(eq(facebookConfigs.isActive, true))
          .orderBy(desc(facebookConfigs.createdAt))
          .limit(1)
        if (!config[0]) return null
        return config[0] as FacebookConfig
      }

      const config = await db
        .select()
        .from(facebookConfigs)
        .where(
          and(
            eq(facebookConfigs.isActive, true),
            eq(facebookConfigs.siteId, siteId),
          ),
        )
        .orderBy(desc(facebookConfigs.createdAt))
        .limit(1)

      if (!config[0]) {
        return null
      }
      return config[0] as FacebookConfig
    } catch (error) {
      console.error(
        `Error getting Facebook config for siteId ${siteIdInput}:`,
        error,
      )
      return null
    }
  }

  async getAllFacebookConfigs(): Promise<FacebookConfig[]> {
    try {
      const db = this.getDb()
      const configs = await db
        .select()
        .from(facebookConfigs)
        .orderBy(desc(facebookConfigs.createdAt))

      return configs as FacebookConfig[]
    } catch (error) {
      console.error('Error getting all Facebook configs:', error)
      return []
    }
  }

  async saveFacebookConfig(
    config: Omit<FacebookConfig, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<number> {
    try {
      const db = this.getDb()

      if (config.siteId === undefined) {
        throw new Error('siteId is required to save Facebook configuration.')
      }

      // Deactivate existing active configs for the specific siteId
      await db
        .update(facebookConfigs)
        .set({ isActive: false })
        .where(
          and(
            eq(facebookConfigs.siteId, config.siteId),
            eq(facebookConfigs.isActive, true),
          ),
        )

      // Insert new config, ensuring it's active
      const newConfig = await db
        .insert(facebookConfigs)
        .values({
          ...config,
          isActive: true, // Ensure new config is active
        })
        .returning({ id: facebookConfigs.id })

      return newConfig[0].id
    } catch (error) {
      console.error('Error saving Facebook config:', error)
      return 0
    }
  }

  // Twilio Message Logging
  async logTwilioMessage(
    message: Omit<TwilioDbMessage, 'id' | 'createdAt' | 'updatedAt'> & {
      id: string
    }, // id (messageSid) is required from Twilio
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot log Twilio message',
      )
      return
    }
    try {
      await this.db
        .insert(twilioMessages)
        .values({
          ...message,
          // Ensure timestamp is a Date object if it's coming as string/number
          timestamp: new Date(message.timestamp),
        })
        .onConflictDoNothing() // Don't overwrite if message already exists
    } catch (error) {
      console.error('Error logging Twilio message:', error)
    }
  }

  // Update Twilio message media URL (for S3 processing)
  async updateTwilioMessageMediaUrl(
    messageId: string,
    mediaUrl: string,
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot update Twilio message',
      )
      return
    }
    try {
      await this.db
        .update(twilioMessages)
        .set({
          mediaUrl,
          updatedAt: new Date(),
        })
        .where(eq(twilioMessages.id, messageId))
    } catch (error) {
      console.error('Error updating Twilio message media URL:', error)
    }
  }

  // Admin User Methods (using unified User table)
  async getAdminByUsername(
    username: string,
    siteId?: string,
  ): Promise<AdminUser | null> {
    if (!this.db) {
      console.error('Database client not initialized - cannot get admin user')
      return null
    }

    const conditions = [
      eq(users.username, username),
      or(
        sql`'ADMIN' = ANY(${users.roles})`,
        sql`'EDITOR' = ANY(${users.roles})`,
        sql`'SUPERADMIN' = ANY(${users.roles})`,
      ),
    ]

    // Add siteId filter if provided
    if (siteId) {
      conditions.push(eq(users.siteId, Number.parseInt(siteId)))
    }

    const user = await this.db
      .select()
      .from(users)
      .where(and(...conditions))
      .limit(1)

    if (!user[0]) {
      return null
    }

    // Return as AdminUser for backward compatibility - use primary admin role
    const primaryRole = user[0].roles.includes(UserRole.ADMIN)
      ? UserRole.ADMIN
      : (user[0].roles[0] as UserRole)
    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email,
      passwordHash: user[0].passwordHash,
      firstName: user[0].firstName,
      lastName: user[0].lastName,
      roles: user[0].roles as UserRole[],
      role: primaryRole, // For backward compatibility
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt || undefined,
      lastLoginAt: user[0].lastLoginAt || undefined,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    } as AdminUser
  }

  async updateAdminLastLogin(userId: number): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot update admin last login',
      )
      return
    }

    await this.db
      .update(users)
      .set({ lastLoginAt: new Date() })
      .where(eq(users.id, userId))
  }

  // WhatsApp Message Logging
  async logWhatsAppMessage(
    id: string,
    fromNumber: string,
    toNumber: string,
    type: string,
    content: string,
    _direction: 'inbound' | 'outbound',
    mediaUrl?: string,
    sessionId?: string,
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot log WhatsApp message',
      )
      return
    }

    await this.db
      .insert(whatsappMessages)
      .values({
        id,
        siteId: 1, // Default site
        from: fromNumber,
        to: toNumber,
        type,
        content,
        mediaUrl: mediaUrl || null,
        sessionId: sessionId || null,
        timestamp: new Date(),
      })
      .onConflictDoNothing()
  }

  // Facebook Message Logging
  async logFacebookMessage(
    id: string,
    from: string,
    to: string,
    type: string,
    content: string,
    direction: 'inbound' | 'outbound',
    mediaUrl?: string,
    sessionId?: string,
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot log Facebook message',
      )
      return
    }

    try {
      await this.db
        .insert(facebookMessages)
        .values({
          id,
          siteId: 1, // Default site
          from,
          to,
          type,
          content,
          mediaUrl: mediaUrl || null,
          sessionId: sessionId || null,
          timestamp: new Date(),
        })
        .onConflictDoNothing()
    } catch (error) {
      console.error('Error logging Facebook message:', error)
    }
  }

  async getWhatsAppMessageHistory(phoneNumber: string, limit = 50) {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot get message history',
      )
      return []
    }

    const messages = await this.db
      .select()
      .from(whatsappMessages)
      .where(
        or(
          eq(whatsappMessages.from, phoneNumber),
          eq(whatsappMessages.to, phoneNumber),
        ),
      )
      .orderBy(desc(whatsappMessages.timestamp))
      .limit(limit)

    return messages
  }

  async getFacebookMessageHistory(userId: string, limit = 50) {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot get Facebook message history',
      )
      return []
    }

    const messages = await this.db
      .select()
      .from(facebookMessages)
      .where(
        or(eq(facebookMessages.from, userId), eq(facebookMessages.to, userId)),
      )
      .orderBy(desc(facebookMessages.timestamp))
      .limit(limit)

    return messages
  }

  // Agent User Methods (using unified User table)
  async getAgentByUsername(
    username: string,
    siteId?: string,
  ): Promise<AgentUser | null> {
    if (!this.db) {
      console.error('Database client not initialized - cannot get agent user')
      return null
    }

    const conditions = [
      eq(users.username, username),
      or(
        sql`'AGENT' = ANY(${users.roles})`,
        sql`'SUPERVISOR' = ANY(${users.roles})`,
      ),
    ]

    // Add siteId filter if provided
    if (siteId) {
      conditions.push(eq(users.siteId, Number.parseInt(siteId)))
    }

    const user = await this.db
      .select()
      .from(users)
      .where(and(...conditions))
      .limit(1)

    if (!user[0]) {
      return null
    }

    // Determine primary agent role for backward compatibility
    const primaryRole = user[0].roles.includes('SUPERVISOR')
      ? 'supervisor'
      : 'agent'

    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email || '',
      passwordHash: user[0].passwordHash, // Include passwordHash for authentication
      firstName: user[0].firstName || '',
      lastName: user[0].lastName || '',
      role: primaryRole,
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt || undefined,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    }
  }

  async getAgentById(agentId: number): Promise<AgentUser | null> {
    if (!this.db) {
      console.error('Database client not initialized - cannot get agent user')
      return null
    }

    const user = await this.db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, agentId),
          or(
            sql`'AGENT' = ANY(${users.roles})`,
            sql`'SUPERVISOR' = ANY(${users.roles})`,
          ),
        ),
      )
      .limit(1)

    if (!user[0]) {
      return null
    }

    // Determine primary agent role for backward compatibility
    const primaryRole = user[0].roles.includes('SUPERVISOR')
      ? 'supervisor'
      : 'agent'

    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email || '',
      firstName: user[0].firstName || '',
      lastName: user[0].lastName || '',
      role: primaryRole,
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt || undefined,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    }
  }

  async updateAgentLastSeen(agentId: number): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot update agent last seen',
      )
      return
    }

    await this.db
      .update(users)
      .set({ lastSeenAt: new Date() })
      .where(eq(users.id, agentId))
  }

  async updateAgentOnlineStatus(
    agentId: number,
    isOnline: boolean,
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot update agent online status',
      )
      return
    }

    await this.db.update(users).set({ isOnline }).where(eq(users.id, agentId))
  }

  async getAllAgents(): Promise<AgentUser[]> {
    if (!this.db) {
      console.error('Database client not initialized - cannot get agents list')
      return []
    }

    const usersList = await this.db
      .select()
      .from(users)
      .where(
        or(
          sql`'AGENT' = ANY(${users.roles})`,
          sql`'SUPERVISOR' = ANY(${users.roles})`,
        ),
      )
      .orderBy(desc(users.createdAt))

    return usersList.map((user) => {
      // Determine primary agent role for backward compatibility
      const primaryRole = user.roles.includes('SUPERVISOR')
        ? 'supervisor'
        : 'agent'

      return {
        id: user.id,
        username: user.username,
        email: user.email || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: primaryRole,
        isActive: user.isActive,
        isOnline: user.isOnline,
        lastSeenAt: user.lastSeenAt || undefined,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    })
  }

  async getAgentActiveSessions(agentId: number): Promise<
    {
      id: string
      userId?: string
      platform: string
      platformId?: string
      status: string
      isHandedOver: boolean
      createdAt: Date
      lastMessageAt?: Date
      messages: any[]
    }[]
  > {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot get agent active sessions',
      )
      return []
    }

    try {
      // Get active sessions assigned to this agent
      const sessions = await this.db
        .select({
          sessionId: sessionAssignments.sessionId,
          assignedAt: sessionAssignments.assignedAt,
          sessionUserId: chatSessions.userId,
          sessionPlatform: chatSessions.platform,
          sessionPlatformId: chatSessions.platformId,
          sessionStatus: chatSessions.status,
          sessionIsHandedOver: chatSessions.isHandedOver,
          sessionCreatedAt: chatSessions.createdAt,
          sessionLastMessageAt: chatSessions.lastMessageAt,
        })
        .from(sessionAssignments)
        .innerJoin(
          chatSessions,
          eq(sessionAssignments.sessionId, chatSessions.id),
        )
        .where(
          and(
            eq(sessionAssignments.agentId, agentId),
            eq(sessionAssignments.status, 'active'),
            eq(chatSessions.status, 'active'),
          ),
        )
        .orderBy(desc(sessionAssignments.assignedAt))

      // For each session, get message count
      const sessionsWithMessages = await Promise.all(
        sessions.map(async (session) => {
          if (!this.db) {
            return {
              id: session.sessionId,
              userId: session.sessionUserId || undefined,
              platform: session.sessionPlatform,
              platformId: session.sessionPlatformId || undefined,
              status: session.sessionStatus,
              isHandedOver: session.sessionIsHandedOver,
              createdAt: session.sessionCreatedAt,
              lastMessageAt: session.sessionLastMessageAt || undefined,
              messages: [],
            }
          }

          const messageCount = await this.db
            .select({ count: sql<number>`count(*)` })
            .from(chatMessages)
            .where(eq(chatMessages.sessionId, session.sessionId))

          return {
            id: session.sessionId,
            userId: session.sessionUserId || undefined,
            platform: session.sessionPlatform,
            platformId: session.sessionPlatformId || undefined,
            status: session.sessionStatus,
            isHandedOver: session.sessionIsHandedOver,
            createdAt: session.sessionCreatedAt,
            lastMessageAt: session.sessionLastMessageAt || undefined,
            messages: Array(messageCount[0]?.count || 0).fill({}), // Create array with correct length for compatibility
          }
        }),
      )

      return sessionsWithMessages
    } catch (error) {
      console.error('Error getting agent active sessions:', error)
      return []
    }
  }

  // Chat Session Methods
  async createChatSession(
    sessionId: string,
    platform = 'web',
    platformId?: string,
    userId?: string,
    botId?: number,
  ): Promise<void> {
    try {
      const db = this.getDb()

      await db.insert(chatSessions).values({
        id: sessionId,
        siteId: 1, // Default site ID for now
        userId,
        platform,
        platformId,
        status: 'active',
        isHandedOver: false,
        botId,
      })
    } catch (error) {
      console.error('Error creating chat session:', error)
    }
  }

  async getAllChatSessions(limit = 50): Promise<any[]> {
    try {
      const db = this.getDb()

      // Get all sessions with basic info and message count
      const sessions = await db
        .select({
          id: chatSessions.id,
          userId: chatSessions.userId,
          platform: chatSessions.platform,
          platformId: chatSessions.platformId,
          status: chatSessions.status,
          isHandedOver: chatSessions.isHandedOver,
          createdAt: chatSessions.createdAt,
          lastMessageAt: chatSessions.lastMessageAt,
          messageCount: count(chatMessages.id),
        })
        .from(chatSessions)
        .leftJoin(chatMessages, eq(chatSessions.id, chatMessages.sessionId))
        .groupBy(
          chatSessions.id,
          chatSessions.userId,
          chatSessions.platform,
          chatSessions.platformId,
          chatSessions.status,
          chatSessions.isHandedOver,
          chatSessions.createdAt,
          chatSessions.lastMessageAt,
        )
        .orderBy(desc(chatSessions.lastMessageAt), desc(chatSessions.createdAt))
        .limit(limit)

      return sessions.map((session) => ({
        id: session.id,
        userId: session.userId || undefined,
        platform: session.platform,
        platformId: session.platformId || undefined,
        status: session.status,
        isHandedOver: session.isHandedOver,
        createdAt: session.createdAt,
        lastMessageAt: session.lastMessageAt || undefined,
        messageCount: Number(session.messageCount) || 0,
      }))
    } catch (error) {
      console.error('Error getting all chat sessions:', error)
      return []
    }
  }

  async getChatSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const db = this.getDb()

      // Get session with messages
      const session = await db
        .select()
        .from(chatSessions)
        .where(eq(chatSessions.id, sessionId))
        .limit(1)

      if (!session[0]) {
        return null
      }

      // Get messages for this session
      const messages = await db
        .select({
          id: chatMessages.id,
          role: chatMessages.role,
          content: chatMessages.content,
          timestamp: chatMessages.timestamp,
          imageUrl: chatMessages.imageUrl,
          audioUrl: chatMessages.audioUrl,
          fileUrl: chatMessages.fileUrl,
          fileName: chatMessages.fileName,
          agentId: chatMessages.agentId,
          agentFirstName: users.firstName,
          agentLastName: users.lastName,
        })
        .from(chatMessages)
        .leftJoin(users, eq(chatMessages.agentId, users.id))
        .where(eq(chatMessages.sessionId, sessionId))
        .orderBy(asc(chatMessages.timestamp))

      // Get session assignment
      const assignment = await db
        .select({
          agentId: sessionAssignments.agentId,
          agentUsername: users.username,
          agentEmail: users.email,
          agentFirstName: users.firstName,
          agentLastName: users.lastName,
          agentRoles: users.roles,
          agentIsActive: users.isActive,
          agentIsOnline: users.isOnline,
          agentLastSeenAt: users.lastSeenAt,
          agentCreatedAt: users.createdAt,
          agentUpdatedAt: users.updatedAt,
        })
        .from(sessionAssignments)
        .leftJoin(users, eq(sessionAssignments.agentId, users.id))
        .where(
          and(
            eq(sessionAssignments.sessionId, sessionId),
            eq(sessionAssignments.status, 'active'),
          ),
        )
        .limit(1)

      return {
        id: session[0].id,
        userId: session[0].userId || undefined,
        platform: session[0].platform,
        platformId: session[0].platformId || undefined,
        status: session[0].status,
        isHandedOver: session[0].isHandedOver,
        createdAt: session[0].createdAt,
        lastMessageAt: session[0].lastMessageAt || undefined,
        messages: messages.map((msg) => ({
          id: msg.id,
          role: msg.role as 'system' | 'user' | 'assistant' | 'agent',
          content: msg.content,
          timestamp: msg.timestamp,
          imageUrl: msg.imageUrl || undefined,
          audioUrl: msg.audioUrl || undefined,
          fileUrl: msg.fileUrl || undefined,
          fileName: msg.fileName || undefined,
          agentId: msg.agentId || undefined,
          agentName:
            msg.agentFirstName && msg.agentLastName
              ? `${msg.agentFirstName} ${msg.agentLastName}`
              : undefined,
        })),
        assignedAgent: assignment[0]?.agentUsername
          ? {
              id: assignment[0].agentId,
              username: assignment[0].agentUsername,
              email: assignment[0].agentEmail,
              firstName: assignment[0].agentFirstName,
              lastName: assignment[0].agentLastName,
              role: assignment[0].agentRoles?.includes(UserRole.SUPERVISOR)
                ? 'supervisor'
                : 'agent',
              isActive: assignment[0].agentIsActive ?? false,
              isOnline: assignment[0].agentIsOnline ?? false,
              lastSeenAt: assignment[0].agentLastSeenAt || undefined,
              createdAt: assignment[0].agentCreatedAt ?? new Date(),
              updatedAt: assignment[0].agentUpdatedAt ?? new Date(),
            }
          : undefined,
      }
    } catch (error) {
      console.error('Error getting chat session:', error)
      return null
    }
  }

  async addChatMessage(
    sessionId: string,
    role: 'system' | 'user' | 'assistant' | 'agent',
    content: string,
    agentId?: number,
    imageUrl?: string,
    audioUrl?: string,
    fileUrl?: string,
    fileName?: string,
  ): Promise<string> {
    try {
      const db = this.getDb()

      const message = await db
        .insert(chatMessages)
        .values({
          id: crypto.randomUUID(),
          siteId: 1, // Default site ID for now
          sessionId,
          role,
          content,
          agentId,
          imageUrl,
          audioUrl,
          fileUrl,
          fileName,
        })
        .returning({ id: chatMessages.id })

      // Update session's last message time
      await db
        .update(chatSessions)
        .set({ lastMessageAt: new Date() })
        .where(eq(chatSessions.id, sessionId))

      return message[0].id
    } catch (error) {
      console.error('Error adding chat message:', error)
      return ''
    }
  }

  // Session Assignment Methods
  async assignSessionToAgent(
    sessionId: string,
    agentId: number,
  ): Promise<number> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot assign session to agent',
      )
      return 0
    }

    const assignment = await this.db
      .insert(sessionAssignments)
      .values({
        sessionId,
        agentId,
        status: 'active',
      })
      .returning({ id: sessionAssignments.id })

    // Update session handover status
    await this.db
      .update(chatSessions)
      .set({ isHandedOver: true })
      .where(eq(chatSessions.id, sessionId))

    return assignment[0].id
  }

  async completeSessionAssignment(sessionId: string): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot complete session assignment',
      )
      return
    }

    await this.db
      .update(sessionAssignments)
      .set({
        status: 'completed',
        completedAt: new Date(),
      })
      .where(
        and(
          eq(sessionAssignments.sessionId, sessionId),
          eq(sessionAssignments.status, 'active'),
        ),
      )

    // Update session handover status
    await this.db
      .update(chatSessions)
      .set({ isHandedOver: false })
      .where(eq(chatSessions.id, sessionId))
  }

  // Handover Request Methods
  async createHandoverRequest(
    sessionId: string,
    requestedBy: 'user' | 'bot' | 'admin',
    reason?: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal',
  ): Promise<number> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot create handover request',
      )
      return 0
    }

    const request = await this.db
      .insert(handoverRequests)
      .values({
        sessionId,
        requestedBy,
        reason,
        priority,
        status: 'pending',
      })
      .returning({ id: handoverRequests.id })

    return request[0].id
  }

  async getPendingHandoverRequests(): Promise<HandoverRequest[]> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot get handover requests',
      )
      return []
    }

    const requests = await this.db
      .select({
        id: handoverRequests.id,
        sessionId: handoverRequests.sessionId,
        requestedBy: handoverRequests.requestedBy,
        reason: handoverRequests.reason,
        priority: handoverRequests.priority,
        status: handoverRequests.status,
        assignedTo: handoverRequests.assignedTo,
        requestedAt: handoverRequests.requestedAt,
        assignedAt: handoverRequests.assignedAt,
        completedAt: handoverRequests.completedAt,
        agentId: users.id,
        agentUsername: users.username,
        agentEmail: users.email,
        agentFirstName: users.firstName,
        agentLastName: users.lastName,
        agentRoles: users.roles,
        agentIsActive: users.isActive,
        agentIsOnline: users.isOnline,
        agentLastSeenAt: users.lastSeenAt,
        agentCreatedAt: users.createdAt,
        agentUpdatedAt: users.updatedAt,
      })
      .from(handoverRequests)
      .leftJoin(users, eq(handoverRequests.assignedTo, users.id))
      .where(eq(handoverRequests.status, 'pending'))
      .orderBy(
        desc(handoverRequests.priority),
        asc(handoverRequests.requestedAt),
      )

    return requests.map((req) => ({
      id: req.id,
      sessionId: req.sessionId,
      requestedBy: req.requestedBy as 'user' | 'bot' | 'admin',
      reason: req.reason || undefined,
      priority: req.priority as 'low' | 'normal' | 'high' | 'urgent',
      status: req.status as 'pending' | 'assigned' | 'completed' | 'cancelled',
      assignedTo: req.assignedTo || undefined,
      requestedAt: req.requestedAt,
      assignedAt: req.assignedAt || undefined,
      completedAt: req.completedAt || undefined,
      agent:
        req.agentUsername && req.agentId
          ? {
              id: req.agentId,
              username: req.agentUsername,
              email: req.agentEmail,
              firstName: req.agentFirstName,
              lastName: req.agentLastName,
              role: req.agentRoles?.includes(UserRole.SUPERVISOR)
                ? 'supervisor'
                : 'agent',
              isActive: req.agentIsActive ?? false,
              isOnline: req.agentIsOnline ?? false,
              lastSeenAt: req.agentLastSeenAt || undefined,
              createdAt: req.agentCreatedAt ?? new Date(),
              updatedAt: req.agentUpdatedAt ?? new Date(),
            }
          : undefined,
    }))
  }

  async assignHandoverRequest(
    requestId: number,
    agentId: number,
  ): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot assign handover request',
      )
      return
    }

    await this.db
      .update(handoverRequests)
      .set({
        assignedTo: agentId,
        status: 'assigned',
        assignedAt: new Date(),
      })
      .where(eq(handoverRequests.id, requestId))
  }

  async completeHandoverRequest(requestId: number): Promise<void> {
    if (!this.db) {
      console.error(
        'Database client not initialized - cannot complete handover request',
      )
      return
    }

    await this.db
      .update(handoverRequests)
      .set({
        status: 'completed',
        completedAt: new Date(),
      })
      .where(eq(handoverRequests.id, requestId))
  }

  // Unified User Management Methods
  async createUser(data: UserCreationRequest): Promise<User | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create user.')
      return null
    }

    const hashedPassword = bcrypt.hashSync(data.password, 10)

    const user = await this.db
      .insert(users)
      .values({
        siteId: 1, // Default site
        username: data.username,
        email: data.email,
        passwordHash: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        roles: data.roles,
        isActive: data.isActive ?? true,
      })
      .returning()

    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email,
      passwordHash: user[0].passwordHash,
      firstName: user[0].firstName,
      lastName: user[0].lastName,
      roles: user[0].roles as UserRole[],
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt,
      lastLoginAt: user[0].lastLoginAt,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    }
  }

  async getUserById(id: number): Promise<User | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get user.')
      return null
    }

    const user = await this.db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1)

    if (!user[0]) {
      return null
    }

    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email,
      passwordHash: user[0].passwordHash,
      firstName: user[0].firstName,
      lastName: user[0].lastName,
      roles: user[0].roles as UserRole[],
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt,
      lastLoginAt: user[0].lastLoginAt,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    }
  }

  async updateUser(id: number, data: UserUpdateRequest): Promise<User | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update user.')
      return null
    }

    const updateData: Partial<typeof users.$inferInsert> = { ...data }
    if (data.password) {
      updateData.passwordHash = bcrypt.hashSync(data.password, 10)
      // Remove password from updateData
      const { password, ...dataWithoutPassword } = data
      Object.assign(updateData, dataWithoutPassword)
    }

    const user = await this.db
      .update(users)
      .set(updateData)
      .where(eq(users.id, id))
      .returning()

    if (!user[0]) {
      return null
    }

    return {
      id: user[0].id,
      username: user[0].username,
      email: user[0].email,
      passwordHash: user[0].passwordHash,
      firstName: user[0].firstName,
      lastName: user[0].lastName,
      roles: user[0].roles as UserRole[],
      isActive: user[0].isActive,
      isOnline: user[0].isOnline,
      lastSeenAt: user[0].lastSeenAt,
      lastLoginAt: user[0].lastLoginAt,
      createdAt: user[0].createdAt,
      updatedAt: user[0].updatedAt,
    }
  }

  async deleteUser(id: number): Promise<User | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete user.')
      return null
    }

    try {
      const deletedUser = await this.db
        .delete(users)
        .where(eq(users.id, id))
        .returning()

      if (!deletedUser[0]) {
        return null
      }

      return {
        id: deletedUser[0].id,
        username: deletedUser[0].username,
        email: deletedUser[0].email,
        passwordHash: deletedUser[0].passwordHash,
        firstName: deletedUser[0].firstName,
        lastName: deletedUser[0].lastName,
        roles: deletedUser[0].roles as UserRole[],
        isActive: deletedUser[0].isActive,
        isOnline: deletedUser[0].isOnline,
        lastSeenAt: deletedUser[0].lastSeenAt,
        lastLoginAt: deletedUser[0].lastLoginAt,
        createdAt: deletedUser[0].createdAt,
        updatedAt: deletedUser[0].updatedAt,
      }
    } catch (error: unknown) {
      console.error('Error deleting user:', error)
      return null
    }
  }

  // S3Configuration Methods
  async createS3Configuration(
    data: S3ConfigurationCreationRequest,
  ): Promise<S3Configuration | null> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot create S3 configuration.',
      )
      return null
    }

    const result = await this.db
      .insert(s3Configurations)
      .values(data)
      .returning()

    return result[0] || null
  }

  async getAllS3Configurations(): Promise<S3Configuration[]> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - returning empty S3 configurations list.',
      )
      return []
    }

    return this.db
      .select()
      .from(s3Configurations)
      .orderBy(asc(s3Configurations.serviceName))
  }

  async getS3ConfigurationById(id: number): Promise<S3Configuration | null> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot get S3 configuration.',
      )
      return null
    }

    const result = await this.db
      .select()
      .from(s3Configurations)
      .where(eq(s3Configurations.id, id))
      .limit(1)

    return result[0] || null
  }

  async getS3ConfigurationByServiceName(
    serviceName: string,
  ): Promise<S3Configuration | null> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot get S3 configuration.',
      )
      return null
    }

    const result = await this.db
      .select()
      .from(s3Configurations)
      .where(eq(s3Configurations.serviceName, serviceName))
      .limit(1)

    return result[0] || null
  }

  async getS3ConfigurationBySiteId(
    siteId: number,
  ): Promise<S3Configuration | null> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot get S3 configuration.',
      )
      return null
    }

    const result = await this.db
      .select()
      .from(s3Configurations)
      .where(eq(s3Configurations.siteId, siteId))
      .orderBy(desc(s3Configurations.createdAt))
      .limit(1)

    return result[0] || null
  }

  async updateS3Configuration(
    id: number,
    data: Partial<S3ConfigurationCreationRequest>,
  ): Promise<S3Configuration | null> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot update S3 configuration.',
      )
      return null
    }

    const updateData: any = {}
    if (data.serviceName !== undefined)
      updateData.serviceName = data.serviceName
    if (data.accessKeyId !== undefined)
      updateData.accessKeyId = data.accessKeyId
    if (data.secretAccessKey !== undefined)
      updateData.secretAccessKey = data.secretAccessKey
    if (data.bucketName !== undefined) updateData.bucketName = data.bucketName
    if (data.region !== undefined) updateData.region = data.region
    if (data.endpointUrl !== undefined)
      updateData.endpointUrl = data.endpointUrl
    updateData.updatedAt = new Date()

    const result = await this.db
      .update(s3Configurations)
      .set(updateData)
      .where(eq(s3Configurations.id, id))
      .returning()

    return result[0] || null
  }

  async deleteS3Configuration(id: number): Promise<boolean> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - cannot delete S3 configuration.',
      )
      return false
    }

    const result = await this.db
      .delete(s3Configurations)
      .where(eq(s3Configurations.id, id))
      .returning()

    return result.length > 0
  }

  // Collection Methods
  async createCollection(data: {
    name: string
    status?: string
  }): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create collection.')
      return null
    }

    const result = await this.db
      .insert(collections)
      .values({
        siteId: 1, // Default site ID
        name: data.name,
        status: (data.status as any) || 'ACTIVE',
      })
      .returning()

    return result[0] || null
  }

  async getAllCollections(): Promise<any[]> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - returning empty collections list.',
      )
      return []
    }

    // Get collections with document counts
    const collectionsData = await this.db
      .select({
        id: collections.id,
        siteId: collections.siteId,
        name: collections.name,
        status: collections.status,
        createdAt: collections.createdAt,
        updatedAt: collections.updatedAt,
        documentCount: sql<number>`count(${documents.id})`,
      })
      .from(collections)
      .leftJoin(documents, eq(collections.id, documents.collectionId))
      .groupBy(collections.id, collections.siteId, collections.name, collections.status, collections.createdAt, collections.updatedAt)
      .orderBy(asc(collections.name))

    // Transform the data to match the expected format
    return collectionsData.map(collection => ({
      ...collection,
      _count: {
        documents: collection.documentCount,
      },
    }))
  }

  async getCollectionById(id: number): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get collection.')
      return null
    }

    const result = await this.db
      .select()
      .from(collections)
      .where(eq(collections.id, id))

    return result[0] || null
  }

  async updateCollection(
    id: number,
    data: { name?: string; status?: string },
  ): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update collection.')
      return null
    }

    const updateData: any = {}
    if (data.name) updateData.name = data.name
    if (data.status) updateData.status = data.status
    updateData.updatedAt = new Date()

    const result = await this.db
      .update(collections)
      .set(updateData)
      .where(eq(collections.id, id))
      .returning()

    return result[0] || null
  }

  async deleteCollection(id: number): Promise<boolean> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete collection.')
      return false
    }

    const result = await this.db
      .delete(collections)
      .where(eq(collections.id, id))
      .returning()

    return result.length > 0
  }

  // Document Methods
  async createDocument(data: {
    collectionId: number;
    s3ConfigurationId: number;
    s3Key: string;
    filename: string;
    filesize?: number | null;
    mimetype?: string | null;
  }): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create document.');
      return null;
    }

    const result = await this.db
      .insert(documents)
      .values({
        collectionId: data.collectionId,
        s3ConfigurationId: data.s3ConfigurationId,
        s3Key: data.s3Key,
        filename: data.filename,
        filesize: data.filesize,
        mimetype: data.mimetype,
      })
      .returning()

    return result[0] || null
  }

  async getAllDocuments(page: number = 1, limit: number = 10): Promise<{
    data: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }> {
    if (!this.db) {
      console.warn('Database operations disabled - returning empty documents list.');
      return {
        data: [],
        pagination: { page, limit, total: 0, hasMore: false, totalPages: 0 },
      };
    }

    const offset = (page - 1) * limit;

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(documents);

    const total = totalResult[0]?.count || 0;

    // Get paginated results with joined data
    const documentsData = await this.db
      .select({
        id: documents.id,
        collectionId: documents.collectionId,
        s3ConfigurationId: documents.s3ConfigurationId,
        s3Key: documents.s3Key,
        filename: documents.filename,
        filesize: documents.filesize,
        mimetype: documents.mimetype,
        createdAt: documents.createdAt,
        updatedAt: documents.updatedAt,
        collectionName: collections.name,
        s3ConfigurationServiceName: s3Configurations.serviceName,
      })
      .from(documents)
      .leftJoin(collections, eq(documents.collectionId, collections.id))
      .leftJoin(s3Configurations, eq(documents.s3ConfigurationId, s3Configurations.id))
      .orderBy(desc(documents.createdAt))
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    return {
      data: documentsData,
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages,
      },
    };
  }

  async getDocumentsByCollectionId(collectionId: number, page: number = 1, limit: number = 10): Promise<{
    data: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }> {
    if (!this.db) {
      console.warn('Database operations disabled - returning empty documents list.');
      return {
        data: [],
        pagination: { page, limit, total: 0, hasMore: false, totalPages: 0 },
      };
    }

    const offset = (page - 1) * limit;

    // Get total count for this collection
    const totalResult = await this.db
      .select({ count: count() })
      .from(documents)
      .where(eq(documents.collectionId, collectionId));

    const total = totalResult[0]?.count || 0;

    // Get paginated results with joined data
    const documentsData = await this.db
      .select({
        id: documents.id,
        collectionId: documents.collectionId,
        s3ConfigurationId: documents.s3ConfigurationId,
        s3Key: documents.s3Key,
        filename: documents.filename,
        filesize: documents.filesize,
        mimetype: documents.mimetype,
        createdAt: documents.createdAt,
        updatedAt: documents.updatedAt,
        collectionName: collections.name,
        s3ConfigurationServiceName: s3Configurations.serviceName,
      })
      .from(documents)
      .leftJoin(collections, eq(documents.collectionId, collections.id))
      .leftJoin(s3Configurations, eq(documents.s3ConfigurationId, s3Configurations.id))
      .where(eq(documents.collectionId, collectionId))
      .orderBy(desc(documents.createdAt))
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    return {
      data: documentsData,
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages,
      },
    };
  }

  async getDocumentById(id: number): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get document.');
      return null;
    }

    const result = await this.db
      .select({
        id: documents.id,
        collectionId: documents.collectionId,
        s3ConfigurationId: documents.s3ConfigurationId,
        s3Key: documents.s3Key,
        filename: documents.filename,
        filesize: documents.filesize,
        mimetype: documents.mimetype,
        createdAt: documents.createdAt,
        updatedAt: documents.updatedAt,
        collectionName: collections.name,
        s3ConfigurationServiceName: s3Configurations.serviceName,
      })
      .from(documents)
      .leftJoin(collections, eq(documents.collectionId, collections.id))
      .leftJoin(s3Configurations, eq(documents.s3ConfigurationId, s3Configurations.id))
      .where(eq(documents.id, id))
      .limit(1);

    return result[0] || null;
  }

  async updateDocument(
    id: number,
    data: { filename?: string },
  ): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update document.');
      return null;
    }

    const updateData: any = {};
    if (data.filename) updateData.filename = data.filename;
    updateData.updatedAt = new Date();

    const result = await this.db
      .update(documents)
      .set(updateData)
      .where(eq(documents.id, id))
      .returning();

    return result[0] || null;
  }

  async deleteDocument(id: number): Promise<boolean> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete document.');
      return false;
    }

    const result = await this.db
      .delete(documents)
      .where(eq(documents.id, id))
      .returning();

    return result.length > 0;
  }

  // Admin User Management Methods
  async createAdminUser(data: {
    username: string
    password: string
    roles: string[]
    email?: string
    firstName?: string
    lastName?: string
    isActive?: boolean
  }): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create admin user.')
      return null
    }

    // Check if username already exists
    const existingUser = await this.db
      .select()
      .from(users)
      .where(eq(users.username, data.username))

    if (existingUser.length > 0) {
      throw new Error('Username already exists')
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 10)

    const result = await this.db
      .insert(users)
      .values({
        siteId: 1, // Default site ID
        username: data.username,
        email: data.email || null,
        passwordHash: hashedPassword,
        firstName: data.firstName || null,
        lastName: data.lastName || null,
        roles: data.roles,
        isActive: data.isActive ?? true,
      })
      .returning()

    return result[0] || null
  }

  async getAllAdminUsers(): Promise<any[]> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - returning empty admin users list.',
      )
      return []
    }

    return this.db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        roles: users.roles,
        isActive: users.isActive,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .orderBy(asc(users.username))
  }

  async getAdminUserById(id: number): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get admin user.')
      return null
    }

    const result = await this.db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        roles: users.roles,
        isActive: users.isActive,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.id, id))

    return result[0] || null
  }

  async updateAdminUser(
    id: number,
    data: {
      username?: string
      password?: string
      roles?: string[]
      email?: string
      firstName?: string
      lastName?: string
      isActive?: boolean
    },
  ): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update admin user.')
      return null
    }

    const updateData: any = {}
    if (data.username !== undefined) updateData.username = data.username
    if (data.email !== undefined) updateData.email = data.email
    if (data.firstName !== undefined) updateData.firstName = data.firstName
    if (data.lastName !== undefined) updateData.lastName = data.lastName
    if (data.password)
      updateData.passwordHash = await bcrypt.hash(data.password, 10)
    if (data.roles) updateData.roles = data.roles
    if (data.isActive !== undefined) updateData.isActive = data.isActive
    updateData.updatedAt = new Date()
    console.log('updating user', { users, updateData })
    const result = await this.db
      .update(users)
      .set(updateData)
      .where(eq(users.id, id))
      .returning({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        roles: users.roles,
        isActive: users.isActive,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })

    return result[0] || null
  }

  async deleteAdminUser(id: number): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete admin user.')
      return null
    }

    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))
      .returning({
        id: users.id,
        username: users.username,
      })

    return result[0] || null
  }

  async close(): Promise<void> {
    // Database connections are now created per request, no global cleanup needed
  }

  // Site Management Methods
  async getAllSites(): Promise<any[]> {
    if (!this.db) {
      console.warn('Database operations disabled - returning empty sites list.')
      return []
    }

    return this.db.select().from(sites).orderBy(asc(sites.name))
  }

  async getSiteById(id: number): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get site.')
      return null
    }

    const result = await this.db
      .select()
      .from(sites)
      .where(eq(sites.id, id))
      .limit(1)

    return result[0] || null
  }

  async createSite(data: {
    name: string
    code: string
    domains: string[]
    status?: boolean
  }): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create site.')
      return null
    }

    // Check if code already exists
    const existingSite = await this.db
      .select()
      .from(sites)
      .where(eq(sites.code, data.code))

    if (existingSite.length > 0) {
      throw new Error('Site code already exists')
    }

    const result = await this.db
      .insert(sites)
      .values({
        name: data.name,
        code: data.code,
        domains: data.domains,
        status: data.status ?? true,
      })
      .returning()

    return result[0] || null
  }

  async updateSite(
    id: number,
    data: {
      name?: string
      code?: string
      domains?: string[]
      status?: boolean
    },
  ): Promise<any | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update site.')
      return null
    }

    // If updating code, check if it already exists (excluding current site)
    if (data.code) {
      const existingSite = await this.db
        .select()
        .from(sites)
        .where(and(eq(sites.code, data.code), sql`${sites.id} != ${id}`))

      if (existingSite.length > 0) {
        throw new Error('Site code already exists')
      }
    }

    const result = await this.db
      .update(sites)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(sites.id, id))
      .returning()

    return result[0] || null
  }

  async deleteSite(id: number): Promise<boolean> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete site.')
      return false
    }

    // Check if site has users
    const siteUsers = await this.db
      .select()
      .from(users)
      .where(eq(users.siteId, id))
      .limit(1)

    if (siteUsers.length > 0) {
      throw new Error('Cannot delete site with existing users')
    }

    const result = await this.db
      .delete(sites)
      .where(eq(sites.id, id))
      .returning()

    return result.length > 0
  }

  // Session Statistics Methods
  async getSessionStats(): Promise<{
    totalSessions: number
    activeSessions: number
    pendingHandovers: number
    completedToday: number
  }> {
    try {
      const db = this.getDb()
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Count total sessions
      const totalSessionsResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(chatSessions)

      // Count active sessions
      const activeSessionsResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(chatSessions)
        .where(eq(chatSessions.status, 'active'))

      // Count pending handover requests
      const pendingHandoversResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(handoverRequests)
        .where(eq(handoverRequests.status, 'pending'))

      // Count sessions completed today
      const completedTodayResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(sessionAssignments)
        .where(
          and(
            eq(sessionAssignments.status, 'completed'),
            sql`${sessionAssignments.completedAt} >= ${today.toISOString()}`,
          ),
        )

      return {
        totalSessions: totalSessionsResult[0]?.count || 0,
        activeSessions: activeSessionsResult[0]?.count || 0,
        pendingHandovers: pendingHandoversResult[0]?.count || 0,
        completedToday: completedTodayResult[0]?.count || 0,
      }
    } catch (error) {
      console.error('Error getting session stats:', error)
      return {
        totalSessions: 0,
        activeSessions: 0,
        pendingHandovers: 0,
        completedToday: 0,
      }
    }
  }

  async getAllActiveSessions(): Promise<
    {
      id: string
      userId?: string
      platform: string
      platformId?: string
      status: string
      isHandedOver: boolean
      createdAt: Date
      lastMessageAt?: Date
      messageCount: number
    }[]
  > {
    try {
      const db = this.getDb()

      // Get all active sessions with message counts
      const sessions = await db
        .select({
          id: chatSessions.id,
          userId: chatSessions.userId,
          platform: chatSessions.platform,
          platformId: chatSessions.platformId,
          status: chatSessions.status,
          isHandedOver: chatSessions.isHandedOver,
          createdAt: chatSessions.createdAt,
          lastMessageAt: chatSessions.lastMessageAt,
          messageCount: sql<number>`count(${chatMessages.id})`,
        })
        .from(chatSessions)
        .leftJoin(chatMessages, eq(chatSessions.id, chatMessages.sessionId))
        .where(eq(chatSessions.status, 'active'))
        .groupBy(
          chatSessions.id,
          chatSessions.userId,
          chatSessions.platform,
          chatSessions.platformId,
          chatSessions.status,
          chatSessions.isHandedOver,
          chatSessions.createdAt,
          chatSessions.lastMessageAt,
        )
        .orderBy(desc(chatSessions.lastMessageAt), desc(chatSessions.createdAt))

      return sessions.map((session) => ({
        id: session.id,
        userId: session.userId || undefined,
        platform: session.platform,
        platformId: session.platformId || undefined,
        status: session.status,
        isHandedOver: session.isHandedOver,
        createdAt: new Date(session.createdAt),
        lastMessageAt: session.lastMessageAt
          ? new Date(session.lastMessageAt)
          : undefined,
        messageCount: session.messageCount || 0,
      }))
    } catch (error) {
      console.error('Error getting all active sessions:', error)
      return []
    }
  }

  // This method is already defined above with limit parameter

  // Service Management Methods
  async createService(
    data: ServiceCreationRequest,
  ): Promise<ServiceConfiguration | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot create service.')
      return null
    }

    const result = await this.db
      .insert(services)
      .values({
        siteId: 1, // Default site ID - should be passed from context
        name: data.name,
        type: data.type,
        description: data.description || null,
        isActive: data.isActive ?? true,
        configuration: JSON.stringify(data.configuration),
      })
      .returning()

    return result[0] || null
  }

  async getAllServices(): Promise<ServiceConfiguration[]> {
    if (!this.db) {
      console.warn(
        'Database operations disabled - returning empty services list.',
      )
      return []
    }

    return this.db.select().from(services).orderBy(asc(services.name))
  }

  async getServiceById(id: number): Promise<ServiceConfiguration | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot get service.')
      return null
    }

    const result = await this.db
      .select()
      .from(services)
      .where(eq(services.id, id))
      .limit(1)

    return result[0] || null
  }

  async updateService(
    id: number,
    data: ServiceUpdateRequest,
  ): Promise<ServiceConfiguration | null> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot update service.')
      return null
    }

    const updateData: any = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined)
      updateData.description = data.description
    if (data.isActive !== undefined) updateData.isActive = data.isActive
    if (data.configuration !== undefined)
      updateData.configuration = JSON.stringify(data.configuration)
    updateData.updatedAt = new Date()

    const result = await this.db
      .update(services)
      .set(updateData)
      .where(eq(services.id, id))
      .returning()

    return result[0] || null
  }

  async deleteService(id: number): Promise<boolean> {
    if (!this.db) {
      console.warn('Database operations disabled - cannot delete service.')
      return false
    }

    const result = await this.db
      .delete(services)
      .where(eq(services.id, id))
      .returning()

    return result.length > 0
  }

  // Contact Management Methods
  async getContacts(
    siteId: number,
    options: {
      page?: number
      limit?: number
      search?: string
      type?: string
      department?: string
    } = {},
  ): Promise<{
    contacts: Contact[]
    pagination: {
      page: number
      limit: number
      total: number
      hasMore: boolean
      totalPages: number
    }
  }> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    const { page = 1, limit = 12, search, type, department } = options
    const offset = (page - 1) * limit

    // Build where conditions
    const conditions = [eq(contacts.siteId, siteId)]

    if (search) {
      conditions.push(
        or(
          ilike(contacts.name, `%${search}%`),
          ilike(contacts.email, `%${search}%`),
          ilike(contacts.department, `%${search}%`),
        ),
      )
    }

    if (type && type !== 'all') {
      conditions.push(eq(contacts.type, type))
    }

    if (department && department !== 'all') {
      conditions.push(ilike(contacts.department, `%${department}%`))
    }

    const whereClause = and(...conditions)

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(contacts)
      .where(whereClause)

    const total = totalResult[0]?.count || 0

    // Get paginated results
    const contactsList = await this.db
      .select()
      .from(contacts)
      .where(whereClause)
      .orderBy(desc(contacts.createdAt))
      .limit(limit)
      .offset(offset)

    const totalPages = Math.ceil(total / limit)
    const hasMore = page < totalPages

    return {
      contacts: contactsList as Contact[],
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages,
      },
    }
  }

  async getContactById(siteId: number, id: number): Promise<Contact | null> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    const result = await this.db
      .select()
      .from(contacts)
      .where(and(eq(contacts.id, id), eq(contacts.siteId, siteId)))
      .limit(1)

    return result[0] ? (result[0] as Contact) : null
  }

  async createContact(
    siteId: number,
    data: ContactCreationRequest,
  ): Promise<Contact> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    const result = await this.db
      .insert(contacts)
      .values({
        siteId,
        name: data.name,
        title: data.title || null,
        department: data.department || null,
        type: data.type,
        email: data.email,
        phone: data.phone || null,
        mobile: data.mobile || null,
        fax: data.fax || null,
        address: data.address || null,
        city: data.city || null,
        state: data.state || null,
        postcode: data.postcode || null,
        country: data.country || 'Malaysia',
        website: data.website || null,
        workingHours: data.workingHours || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      })
      .returning()

    return result[0] as Contact
  }

  async updateContact(
    siteId: number,
    id: number,
    data: ContactUpdateRequest,
  ): Promise<Contact | null> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    // Check if contact exists and belongs to the site
    const existingContact = await this.getContactById(siteId, id)
    if (!existingContact) {
      return null
    }

    const updateData: any = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.title !== undefined) updateData.title = data.title
    if (data.department !== undefined) updateData.department = data.department
    if (data.type !== undefined) updateData.type = data.type
    if (data.email !== undefined) updateData.email = data.email
    if (data.phone !== undefined) updateData.phone = data.phone
    if (data.mobile !== undefined) updateData.mobile = data.mobile
    if (data.fax !== undefined) updateData.fax = data.fax
    if (data.address !== undefined) updateData.address = data.address
    if (data.city !== undefined) updateData.city = data.city
    if (data.state !== undefined) updateData.state = data.state
    if (data.postcode !== undefined) updateData.postcode = data.postcode
    if (data.country !== undefined) updateData.country = data.country
    if (data.website !== undefined) updateData.website = data.website
    if (data.workingHours !== undefined)
      updateData.workingHours = data.workingHours
    if (data.isActive !== undefined) updateData.isActive = data.isActive
    updateData.updatedAt = new Date()

    const result = await this.db
      .update(contacts)
      .set(updateData)
      .where(and(eq(contacts.id, id), eq(contacts.siteId, siteId)))
      .returning()

    return result[0] ? (result[0] as Contact) : null
  }

  async deleteContact(siteId: number, id: number): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database not initialized')
    }

    const result = await this.db
      .delete(contacts)
      .where(and(eq(contacts.id, id), eq(contacts.siteId, siteId)))
      .returning()

    return result.length > 0
  }

  // Bot Management Methods
  private mapDbBotToBot(dbBot: any): Bot {
    return {
      id: dbBot.id,
      siteId: dbBot.site_id,
      name: dbBot.name,
      slug: dbBot.slug,
      provider: dbBot.provider,
      model: dbBot.model,
      temperature: dbBot.temperature,
      isDefault: dbBot.is_default,
      systemPrompt: dbBot.system_prompt,
      createdAt: new Date(dbBot.created_at),
      updatedAt: new Date(dbBot.updated_at),
    }
  }

  async getBotBySlug(slug: string, siteId?: number): Promise<Bot | null> {
    if (!this.db) {
      throw new Error(
        'Database client not initialized - cannot get bot by slug',
      )
    }

    try {
      const conditions = [eq(bots.slug, slug)]

      if (siteId) {
        conditions.push(eq(bots.siteId, siteId))
      }

      const result = await this.db
        .select()
        .from(bots)
        .where(and(...conditions))
        .limit(1)

      return result[0] ? this.mapDbBotToBot(result[0]) : null
    } catch (error) {
      console.error('Error getting bot by slug:', error)
      throw new Error(`Failed to retrieve bot with slug: ${slug}`)
    }
  }

  async getAllBots(siteId?: number): Promise<Bot[]> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot get all bots')
    }

    try {
      const conditions = []

      if (siteId) {
        conditions.push(eq(bots.siteId, siteId))
      }

      const whereClause = conditions.length > 0 ? and(...conditions) : undefined

      const result = await this.db
        .select()
        .from(bots)
        .where(whereClause)
        .orderBy(asc(bots.name))

      return result.map(this.mapDbBotToBot)
    } catch (error) {
      console.error('Error getting all bots:', error)
      throw new Error('Failed to retrieve bots')
    }
  }

  async getDefaultBot(siteId?: number): Promise<Bot | null> {
    if (!this.db) {
      throw new Error(
        'Database client not initialized - cannot get default bot',
      )
    }

    try {
      const conditions = [eq(bots.isDefault, true)]

      if (siteId) {
        conditions.push(eq(bots.siteId, siteId))
      }

      const result = await this.db
        .select()
        .from(bots)
        .where(and(...conditions))
        .limit(1)

      return result[0] ? this.mapDbBotToBot(result[0]) : null
    } catch (error) {
      console.error('Error getting default bot:', error)
      throw new Error('Failed to retrieve default bot')
    }
  }

  async createBot(botData: {
    siteId: number
    name: string
    slug: string
    provider: string
    model: string
    temperature?: number
    isDefault?: boolean
    systemPrompt?: string
  }): Promise<Bot> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot create bot')
    }

    try {
      // If this is the default bot, unset any existing default
      if (botData.isDefault) {
        await this.db
          .update(bots)
          .set({ isDefault: false })
          .where(eq(bots.siteId, botData.siteId))
      }

      const [result] = await this.db
        .insert(bots)
        .values({
          siteId: botData.siteId,
          name: botData.name,
          slug: botData.slug,
          provider: botData.provider,
          model: botData.model,
          temperature: botData.temperature ?? 0.5,
          isDefault: botData.isDefault ?? false,
          systemPrompt: botData.systemPrompt,
        })
        .returning()

      return this.mapDbBotToBot(result)
    } catch (error) {
      console.error('Error creating bot:', error)
      throw new Error('Failed to create bot')
    }
  }

  async updateBot(
    id: number,
    botData: {
      name?: string
      slug?: string
      provider?: string
      model?: string
      temperature?: number
      isDefault?: boolean
      systemPrompt?: string
    },
  ): Promise<Bot | null> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot update bot')
    }

    try {
      // If setting as default, unset existing default
      if (botData.isDefault) {
        const bot = await this.getBotById(id)
        if (bot) {
          await this.db
            .update(bots)
            .set({ isDefault: false })
            .where(eq(bots.siteId, bot.siteId))
        }
      }

      const [result] = await this.db
        .update(bots)
        .set({ ...botData, updatedAt: new Date() })
        .where(eq(bots.id, id))
        .returning()

      return result ? this.mapDbBotToBot(result) : null
    } catch (error) {
      console.error('Error updating bot:', error)
      throw new Error('Failed to update bot')
    }
  }

  async deleteBot(id: number): Promise<boolean> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot delete bot')
    }

    try {
      const result = await this.db
        .delete(bots)
        .where(eq(bots.id, id))
        .returning()

      return result.length > 0
    } catch (error) {
      console.error('Error deleting bot:', error)
      throw new Error('Failed to delete bot')
    }
  }

  async getBotById(id: number): Promise<Bot | null> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot get bot by ID')
    }

    try {
      const result = await this.db
        .select()
        .from(bots)
        .where(eq(bots.id, id))
        .limit(1)

      return result[0] ? this.mapDbBotToBot(result[0]) : null
    } catch (error) {
      console.error('Error getting bot by ID:', error)
      throw new Error(`Failed to retrieve bot with ID: ${id}`)
    }
  }

  async getBotUsage(id: number): Promise<{
    totalMessages: number
    totalInputTokens: number
    totalOutputTokens: number
  }> {
    if (!this.db) {
      throw new Error('Database client not initialized - cannot get bot usage')
    }

    try {
      const result = await this.db
        .select({
          totalMessages: sql<number>`count(*)`,
          totalInputTokens: sql<number>`sum(${botUsages.inputTokens})`,
          totalOutputTokens: sql<number>`sum(${botUsages.outputTokens})`,
        })
        .from(botUsages)
        .where(eq(botUsages.botId, id))

      const usage = result[0]
      return {
        totalMessages: Number(usage?.totalMessages ?? 0),
        totalInputTokens: Number(usage?.totalInputTokens ?? 0),
        totalOutputTokens: Number(usage?.totalOutputTokens ?? 0),
      }
    } catch (error) {
      console.error('Error getting bot usage:', error)
      throw new Error(`Failed to retrieve usage for bot ID: ${id}`)
    }
  }

  async getBotWithUsage(id: number): Promise<BotWithUsage | null> {
    const bot = await this.getBotById(id)
    if (!bot) return null

    const usage = await this.getBotUsage(id)
    return {
      ...bot,
      totalUsage: usage,
    }
  }

  async getAllBotsWithUsage(siteId?: number): Promise<BotWithUsage[]> {
    const bots = await this.getAllBots(siteId)
    const botsWithUsage = await Promise.all(
      bots.map(async (bot) => {
        const usage = await this.getBotUsage(bot.id)
        return {
          ...bot,
          totalUsage: usage,
        }
      }),
    )
    return botsWithUsage
  }
}

export default DatabaseService
